package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("TransactionContext Tests")
class TransactionContextTest {

    private TransactionContext context;

    @BeforeEach
    void setUp() {
        context = new TransactionContext();
    }

    @Nested
    @DisplayName("Data Storage and Retrieval")
    class DataStorageTest {

        @Test
        @DisplayName("put() and get() should store and retrieve string values")
        void putAndGet_shouldStoreAndRetrieveStringValues() {
            // Arrange
            String key = "test_key";
            String value = "test_value";

            // Act
            context.put(key, value);
            String retrievedValue = context.get(key);

            // Assert
            assertEquals(value, retrievedValue, "Retrieved value should match stored value");
        }

        @Test
        @DisplayName("get() should return null for non-existent keys")
        void get_shouldReturnNullForNonExistentKeys() {
            // Act
            Object result = context.get("non_existent_key");

            // Assert
            assertNull(result, "Should return null for non-existent keys");
        }

        @Test
        @DisplayName("put() should reject null values")
        void put_shouldRejectNullValues() {
            // Arrange
            String key = "null_key";

            // Act & Assert
            assertThrows(
                    NullPointerException.class,
                    () -> context.put(key, null),
                    "Should throw NullPointerException when storing null values");
        }

        @Test
        @DisplayName("put() should reject null keys")
        void put_shouldRejectNullKeys() {
            // Act & Assert
            assertThrows(
                    NullPointerException.class,
                    () -> context.put(null, "value"),
                    "Should throw NullPointerException when storing with null key");
        }

        @Test
        @DisplayName("put() should overwrite existing values")
        void put_shouldOverwriteExistingValues() {
            // Arrange
            String key = "overwrite_key";
            String originalValue = "original_value";
            String newValue = "new_value";

            // Act
            context.put(key, originalValue);
            context.put(key, newValue);
            String retrievedValue = context.get(key);

            // Assert
            assertEquals(newValue, retrievedValue, "Should overwrite existing values");
        }
    }

    @Nested
    @DisplayName("Step Execution Tracking")
    class StepExecutionTest {

        @Test
        @DisplayName("addExecutedStep() should add step to execution list")
        void addExecutedStep_shouldAddStepToExecutionList() {
            // Arrange
            String stepName = "test_step";

            // Act
            context.addExecutedStep(stepName);
            List<String> executedSteps = context.getExecutedSteps();

            // Assert
            assertEquals(1, executedSteps.size(), "Should have one executed step");
            assertEquals(stepName, executedSteps.get(0), "Should contain the added step");
        }

        @Test
        @DisplayName("addExecutedStep() should maintain order of execution")
        void addExecutedStep_shouldMaintainOrderOfExecution() {
            // Arrange
            String step1 = "step_1";
            String step2 = "step_2";
            String step3 = "step_3";

            // Act
            context.addExecutedStep(step1);
            context.addExecutedStep(step2);
            context.addExecutedStep(step3);
            List<String> executedSteps = context.getExecutedSteps();

            // Assert
            assertEquals(3, executedSteps.size(), "Should have three executed steps");
            assertEquals(step1, executedSteps.get(0), "First step should be in first position");
            assertEquals(step2, executedSteps.get(1), "Second step should be in second position");
            assertEquals(step3, executedSteps.get(2), "Third step should be in third position");
        }

        @Test
        @DisplayName("addExecutedStep() should handle null step names")
        void addExecutedStep_shouldHandleNullStepNames() {
            // Act & Assert
            assertDoesNotThrow(
                    () -> context.addExecutedStep(null),
                    "Should not throw exception when adding null step name");

            List<String> executedSteps = context.getExecutedSteps();
            assertEquals(1, executedSteps.size(), "Should have one executed step");
            assertNull(executedSteps.get(0), "Should contain null step name");
        }

        @Test
        @DisplayName("addExecutedStep() should allow duplicate step names")
        void addExecutedStep_shouldAllowDuplicateStepNames() {
            // Arrange
            String stepName = "duplicate_step";

            // Act
            context.addExecutedStep(stepName);
            context.addExecutedStep(stepName);
            List<String> executedSteps = context.getExecutedSteps();

            // Assert
            assertEquals(2, executedSteps.size(), "Should have two executed steps");
            assertEquals(stepName, executedSteps.get(0), "First occurrence should be preserved");
            assertEquals(stepName, executedSteps.get(1), "Second occurrence should be preserved");
        }

        @Test
        @DisplayName("getExecutedSteps() should return defensive copy")
        void getExecutedSteps_shouldReturnDefensiveCopy() {
            // Arrange
            context.addExecutedStep("original_step");

            // Act
            List<String> executedSteps1 = context.getExecutedSteps();
            List<String> executedSteps2 = context.getExecutedSteps();

            // Assert
            assertNotSame(
                    executedSteps1, executedSteps2, "Should return different instances each time");
            assertEquals(executedSteps1, executedSteps2, "Should contain the same data");
        }

        @Test
        @DisplayName("getExecutedSteps() defensive copy should not affect original when modified")
        void getExecutedSteps_defensiveCopyShouldNotAffectOriginal() {
            // Arrange
            context.addExecutedStep("original_step");

            // Act
            List<String> executedSteps = context.getExecutedSteps();
            executedSteps.add("modified_step");

            // Assert
            List<String> originalSteps = context.getExecutedSteps();
            assertEquals(
                    1,
                    originalSteps.size(),
                    "Original list should not be affected by modifications to returned copy");
            assertEquals(
                    "original_step", originalSteps.get(0), "Original step should remain unchanged");
        }

        @Test
        @DisplayName("getExecutedSteps() should return empty list when no steps executed")
        void getExecutedSteps_shouldReturnEmptyListWhenNoStepsExecuted() {
            // Act
            List<String> executedSteps = context.getExecutedSteps();

            // Assert
            assertNotNull(executedSteps, "Should not return null");
            assertTrue(executedSteps.isEmpty(), "Should return empty list when no steps executed");
        }
    }

    @Nested
    @DisplayName("Thread Safety")
    class ThreadSafetyTest {

        @Test
        @DisplayName("Should handle concurrent put operations safely")
        void shouldHandleConcurrentPutOperationsSafely() throws InterruptedException {
            // Arrange
            ExecutorService executor = Executors.newFixedThreadPool(10);
            int numberOfThreads = 10;
            int operationsPerThread = 100;

            // Act
            for (int i = 0; i < numberOfThreads; i++) {
                final int threadId = i;
                executor.submit(
                        () -> {
                            for (int j = 0; j < operationsPerThread; j++) {
                                context.put(
                                        "key_" + threadId + "_" + j, "value_" + threadId + "_" + j);
                            }
                        });
            }

            executor.shutdown();
            assertTrue(
                    executor.awaitTermination(5, TimeUnit.SECONDS),
                    "All threads should complete within timeout");

            // Assert
            // Verify that all values were stored correctly
            for (int i = 0; i < numberOfThreads; i++) {
                for (int j = 0; j < operationsPerThread; j++) {
                    String expectedValue = "value_" + i + "_" + j;
                    String actualValue = context.get("key_" + i + "_" + j);
                    assertEquals(
                            expectedValue,
                            actualValue,
                            "Value should be correctly stored despite concurrent access");
                }
            }
        }

        @Test
        @DisplayName("Should demonstrate concurrent step execution tracking behavior")
        void shouldDemonstrateConcurrentStepExecutionTrackingBehavior()
                throws InterruptedException {
            // Arrange
            ExecutorService executor = Executors.newFixedThreadPool(10);
            int numberOfThreads = 10;
            int stepsPerThread = 50;

            // Act
            for (int i = 0; i < numberOfThreads; i++) {
                final int threadId = i;
                executor.submit(
                        () -> {
                            for (int j = 0; j < stepsPerThread; j++) {
                                context.addExecutedStep("thread_" + threadId + "_step_" + j);
                            }
                        });
            }

            executor.shutdown();
            assertTrue(
                    executor.awaitTermination(5, TimeUnit.SECONDS),
                    "All threads should complete within timeout");

            // Assert
            List<String> executedSteps = context.getExecutedSteps();
            // Note: ArrayList is not thread-safe, so we may lose some steps due to race conditions
            // This test demonstrates the current behavior rather than asserting perfect thread
            // safety
            assertTrue(executedSteps.size() > 0, "Should have at least some steps recorded");
            assertTrue(
                    executedSteps.size() <= numberOfThreads * stepsPerThread,
                    "Should not have more steps than expected");
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesTest {

        @Test
        @DisplayName("Should handle empty context gracefully")
        void shouldHandleEmptyContextGracefully() {
            // Act
            Object nonExistentValue = context.get("non_existent");
            List<String> executedSteps = context.getExecutedSteps();

            // Assert
            assertNull(nonExistentValue, "Should return null for non-existent keys");
            assertNotNull(executedSteps, "Should return non-null list");
            assertTrue(executedSteps.isEmpty(), "Should return empty list when no steps executed");
        }

        @Test
        @DisplayName("Should handle large amounts of data")
        void shouldHandleLargeAmountsOfData() {
            // Arrange
            int dataSize = 1000;

            // Act
            for (int i = 0; i < dataSize; i++) {
                context.put("key_" + i, "value_" + i);
                context.addExecutedStep("step_" + i);
            }

            // Assert
            assertEquals(
                    "value_500",
                    context.get("key_500"),
                    "Should handle large amounts of data correctly");
            List<String> executedSteps = context.getExecutedSteps();
            assertEquals(dataSize, executedSteps.size(), "Should track all executed steps");
            assertEquals(
                    "step_999",
                    executedSteps.get(999),
                    "Should maintain correct order for large datasets");
        }
    }
}
